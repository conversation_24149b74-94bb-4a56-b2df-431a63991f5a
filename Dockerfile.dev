#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

# pull official base image
FROM node:20-alpine AS builder

# set working directory
WORKDIR /app

# search for executable to be run (just encase)
ENV PATH /app/node_modules/.bin:$PATH

#dependencies to run
COPY ./package.json /app/
COPY ./yarn.lock /app/

RUN npm config set registry https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/
RUN yarn config set registry https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/
RUN npm config set "@fortawesome:registry" https://npm.fontawesome.com/
RUN npm config set "//npm.fontawesome.com/:_authToken" 4522F1C0-89F3-455D-88CA-95E6C24B1B20
RUN yarn config set "@fortawesome:registry" https://npm.fontawesome.com/
RUN yarn config set "//npm.fontawesome.com/:_authToken" 4522F1C0-89F3-455D-88CA-95E6C24B1B20

# install app dependencies
RUN yarn install

# copy current directory into the working directory
COPY . /app

ENV CI=true

# remove .env.production and .env.local file
RUN rm -rf .env.production
RUN rm -rf .env.local

ENV NODE_ENV 'development'
ENV BABEL_ENV 'development'

# start app
RUN yarn staging

# nginx setup
FROM nginx:1.23.4-alpine as production

# copy build files to default directory where Nginx serves from
COPY --from=builder /app/build /usr/share/nginx/html

# remove default configuration present
RUN rm /etc/nginx/conf.d/default.conf

COPY azure-deploy/nginx/nginx.conf /etc/nginx/conf.d

EXPOSE 80

# run Nginx with global directives in the background
CMD ["nginx", "-g", "daemon off;"]
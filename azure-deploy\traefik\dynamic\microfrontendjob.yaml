http:
  services:
    redi-microfrontend-job:
      loadBalancer:
        passHostHeader: false
        servers:
          - url: 'https://DOCKER_REPO.internal.{{ env "CONTAINER_APP_ENV_DNS_SUFFIX" }}'
  middlewares:
    redirecthttps:
      redirectScheme:
        scheme: https
    microfrontendjob-stripprefix:
      stripPrefix:
        prefixes:
          - "/microfrontendjob"
        forceSlash: false
  routers:
    microfrontendjobsecure:
      rule: "PathPrefix(`/microfrontendjob`)"
      middlewares:
        - "microfrontendjob-stripprefix"
      service: redi-microfrontend-job


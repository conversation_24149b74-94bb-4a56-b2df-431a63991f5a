declare module "login-redi-types" {
	interface DtoBase {
		deleted?: boolean;
		createdOn?: string;
		createdByName?: string;
		createdById?: string;
		modifiedOn?: string;
		modifiedByName?: string;
		modifiedById?: string;
	}
}

declare module "redi-types" {
	interface DtoBase {
		deleted?: boolean;
		createdOn?: Date;
		createdByName?: string;
		createdById?: string;
		modifiedOn?: Date;
		modifiedByName?: string;
		modifiedById?: string;
	}
}
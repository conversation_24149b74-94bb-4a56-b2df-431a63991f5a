declare module "redi-types" {
    export interface BaseJobCDto extends DtoBase {
        jobId: string;
        jobReference?: string;
        jobDate: Date;
        invoiceNumber?: string;
        insuranceCompanyId?: string;
        insuranceCompanyName?: string;
        carRegistration?: string;
        yard?: string;
        statusCode: JobStatusCodeEnum;
        spotters?: number;
        contractorId?: string;
        contractorName?: string;
        jobTotalIncGst: number;
        contractorPayIncGst: number;
        paySlipId?: string;
    }



    export interface GetJobCDto extends BaseJobCDto {
        fields: { [key: string]: string | number | Date | boolean };
    }

    export interface GetListJobCDto extends BaseJobCDto {

    }

}
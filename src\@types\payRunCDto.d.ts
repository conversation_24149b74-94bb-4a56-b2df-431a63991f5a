declare module "redi-types" {
    export interface BasePayRunCDto extends DtoBase {
        payRunId: Guid;
        fromDate: Date;
        toDate: Date;
        statusCode: string;
        payRunTotalIncGst: number;
        paySlipCount: number;
    }

    export interface GetPayRunCDto extends BasePayRunCDto {

    }

    export interface GetListPayRunCDto extends BasePayRunCDto {

    }

    export interface GetPayRunCDtoExtended extends BasePayRunCDto {

    }

}

declare module "redi-types" {
    export interface BasePaySlipCDto extends DtoBase {
        paySlipId: Guid;
        payRunId: Guid;
        totalIncGst: number;
        gst: number;
        totalDeductions?: number;
        contractorId: Guid;
        contractorName: string;

    }

    export interface GetPaySlipCDto extends BasePaySlipCDto {

    }

    export interface GetListPaySlipCDto extends BasePaySlipCDto {

    }

}
@import "../../config/theme/vars.scss";

.card-header {
  background-color: $primaryBgColor;
  padding: 1rem;
}

.card-body {
  padding: 0;
}

.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;

  .table-title {
    color: $primaryColor;
    font-size: 1.3rem;
    font-weight: 600;
  }

  .action-button {
    display: flex;
    align-items: center;
    color: $primaryColor;
    cursor: pointer;

    svg {
      font-size: 1.2rem;
      margin-right: 0.5rem;
    }

    div {
      font-weight: 500;
      font-size: 1rem;

    }
  }
}

.search-grid {
  display: grid;
  grid-template-columns: minmax(300px, 1fr) auto 1fr;
  align-items: center;
  margin-bottom: 1rem;
  gap: 0.5rem;

  .search-bar-row {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .search-input {
    flex: 1;
    margin-right: 0.5rem;
  }

  .search-filter {
    width: 8rem;
    margin-right: 0.5rem;
  }

  .close-icon {
    padding: 5px;
    cursor: pointer;
  }    

  .refresh-row {
    justify-self: end;
    display: flex;
    align-items: center;
    color: $primaryColor;

    svg { // Refresh icon
      font-size: 1.1rem;
      cursor: pointer;
      color: $primaryColor;
      margin-right: 0.5rem;
    }

    div {
      font-weight: 500;
      font-size: 0.875rem;
      // color: rgba(0, 0, 0, 0.8);
    }
  }
}

.relative {
  position: relative;
  height: 100%;
  width: 100%;
}

.loading-overlay {
  z-index: 1;
  position: absolute;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
}

.table {
  position: relative;
  box-shadow: none !important;

  .center {
    text-align: center;
  }

  .fetch-more-loader {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem 0 1rem 0;
  }

  .table-row {
    &:nth-of-type(even) {
      background-color: $white;
    }

    &:nth-of-type(odd) {
      background-color: #f6f6f6;
    }

    &:hover {
      background-color: #eee;
    }

    &.active {
      background-color: $primaryBgColor;
    }
  }
}

.clickable {
  cursor: pointer;
}
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Alert, IconButton, Snackbar } from '@mui/material';
import React, { useState } from 'react';
import './styles.scss';
import PaySlipService from '../../services/paySlip';
import { faSpinner } from '@fortawesome/pro-duotone-svg-icons';

interface Props {
  paySlipId: string
}

function EmailPDFButton(props: Props) {
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false); // added loading state

  const handleSnackbarClose = () => {
    setSnackbarMessage(null);
  };

  async function buttonClick(event: React.MouseEvent, paySlipId: string) {
    event.stopPropagation();
    setLoading(true); // set loading to true


    let res = await PaySlipService.emailPayslip(paySlipId);
    if (!res.error) {
      setSnackbarMessage('Email sent successfully');
      setShowSnackbar(true);
    } else {
      setSnackbarMessage('Email failed');
      setShowSnackbar(true);
    }
    //set a ten second timer
    setTimeout(() => {
      setLoading(false);
    }, 10000);

  }

  return (
    <>
      <IconButton onClick={(e) => buttonClick(e, props.paySlipId)} disabled={loading}> {/* disable button when loading or timer is active */}
        {loading ?
          <FontAwesomeIcon title='Email PDF' styleName="row-menu-icon" icon={faSpinner} spin />
          :
          <FontAwesomeIcon title='Email PDF' styleName="row-menu-icon" icon="envelope" />
        }
      </IconButton>
      <Snackbar open={showSnackbar}
        autoHideDuration={4000}
        onClose={() => setShowSnackbar(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}>
        <Alert onClose={() => setShowSnackbar(false)} severity={snackbarMessage?.includes('successfully') ? "success" : "error"}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
}

export default EmailPDFButton;
import React, { Suspense } from 'react';

type LazyProps<P> = JSX.IntrinsicAttributes & ((React.PropsWithoutRef<P> & React.RefAttributes<React.Component<P, any, any>>) | React.PropsWithRef<P>);

/**
 * Use: 
 * 
 *      withAsyncLoad(() => import("./../../pages/Home/Home"))
 * 
 * If no default export of Component, use:
 * 
 *      const importCallback = () => import('./../../pages/Home/Home').then(module => ({ default: module.default }));
 *      withAsyncLoad(importCallback);
 * @param importCallback 
 * @returns 
 */
function withAsyncLoad<P>(importCallback: () => Promise<{default: React.ComponentType<P>}>) {

    const Component = React.lazy(importCallback);

    return (props: LazyProps<P>) => (
        <Suspense fallback={<div>Loading...</div>}>
            <Component {...props} />
        </Suspense>
    );
}

export default withAsyncLoad;
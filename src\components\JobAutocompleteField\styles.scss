@import "../../config/theme/vars.scss";

.title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom:0.5rem;

    .search-flex{
    flex:5;
    display: flex;
    }

    .search-field {
        flex: 1;
    }

    .action-button {
        flex: 2;
        display: flex;
        align-items: center;
        color: $primaryColor;
        cursor: pointer;
        margin-left:2rem;

        svg {
            font-size: 1.2rem;
            margin-right: 0.5rem;
        }

        div {
            font-weight: 500;
            font-size: 1rem;
        }
    }
}

.disabled {
    pointer-events: none;
    color: rgba(208, 206, 206, 0.694) !important;
}

.edit-icon {
    display:flex;
    justify-content: center;
    color: $primaryColor;
    padding-left:1rem;
}

.inner-icon {
    display:flex;
    font-size: 1.2rem;
}

.info-tags{
    margin-top: -15px;
    display: flex;
    flex-direction: row;
}

.tag{
    color: $black;
    padding: 10px;
    >span{
        margin-left:0.5rem;
        font-style: italic;
    }
}
.tag:empty{
    display: none;
}
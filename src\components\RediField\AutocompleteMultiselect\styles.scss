@import "../../../config/theme/theme.scss";

.highlight-text {
    font-weight: 700;
    color: red;
 }
 
 .search-text {
    font-weight: 400;
 }
 
 .calendar-button-wrapper {
    margin-top: 22px;
    height: auto;
    display: inline-block;
    margin-left:30px;
    >button {
        border-radius: 8px;
    }
    img {
        padding: 10.82px 15px;
        border: 1px solid blue;
        border-radius: 8px;
        background-color: white;
    }
 }
 
 ._icon-style {
    position: absolute;
    top: 41px;
    right: 15px;
 }
 
 :global {
    .typeahead-underlined-text {
        color: red;
        font-weight:bold;
    }
 }

:global {
.border-1 {
   border: 1px solid black;
}

.border-2 {
   border: 1px solid red;
}
.display-none {
   display: none;
}
}

 .field-tag {
   font-size: 11px;
   &.active {
      background-color: rgba($primaryColor, 0.5);
      >div:first-child {
         color: white !important;
      }
   }
   >div:first-child {
      padding:7px;
      cursor:pointer;
   }
   border-radius: 5px;
   margin:2px;
   position: relative;
   &:hover {
      .delete-tag-icon {
         display:flex;
      }
   }
   >.delete-tag-icon {
      display:none;
      justify-content: center;
      align-items: center;
      background-color: #f80808;
      color: #fff;
      height: 15px;
      width: 15px;
      position: absolute;
      top: 0px;
      right: 0;
      cursor: pointer;
   }
}

.flex-wrap{
   flex-wrap: wrap;
}

.flex {
   display: flex;
}
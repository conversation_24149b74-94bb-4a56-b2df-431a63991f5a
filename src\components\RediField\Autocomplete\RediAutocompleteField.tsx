import * as React from 'react';
import TextField, { TextFieldProps } from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import CircularProgress from '@mui/material/CircularProgress';
import parse from 'autosuggest-highlight/parse';
import match from 'autosuggest-highlight/match';
import { HttpPromise } from 'redi-http';
import { useCallback, useEffect, useRef, useState } from 'react';
import { ListResponseDto } from 'redi-types';
import './styles.scss';

type TValue = { [x: string]: any, isAdded?: boolean };

function RediAutocompleteField(props: AutocompleteProps) {
    const { id, name, error, helperText, label, placeholder, variant, value, fieldDisplayText, fieldValue, initialDisplayText, minLength = 0, autoSelectFirst, disabled, textFieldProps, callService, onChange } = props;
    const [ open, setOpen ] = useState(false);
    const [ options, setOptions ] = useState<readonly TValue[]>(value ? [{ [fieldDisplayText]: initialDisplayText, [fieldValue]: value[fieldValue] }] : []);
    const [ isLoading, setIsLoading ] = useState(false);
    const [ inputValue, setInputValue ] = useState("");
    const isMinLengthMet = minLength <= (inputValue?.length ?? 0);
    const focusedOptionRef = useRef<number | null>();
    const focusedObserver = useRef<MutationObserver | null>();
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);
    const promiseRef = useRef<HttpPromise<ListResponseDto<any>>>();

    const observer = useCallback((node: HTMLLIElement) => {
        if (!autoSelectFirst) { return; }
        focusedObserver.current = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              if (mutation.type === "attributes" && mutation.attributeName === "class") {
                const ele = mutation.target as HTMLLIElement;
                let isFocused = ele.classList.contains("Mui-focused");
                if (isFocused) {
                  let index = ele.getAttribute("data-option-index");
                  if (index) {
                    let position = Number(index);
                    if (position > -1) { focusedOptionRef.current = position; };
                  }
                } else {
                    focusedOptionRef.current = null;
                }
              }
            });
          });
          if (node) {
            focusedObserver.current.observe(node, {
                attributes: true //listen for attribute change on parent element (which is a list ('li') element)
            });
          }
    }, []);

    useEffect(() => {
        const appendCurrentValue = value ? !options.some((option) => value[fieldValue] === option[fieldValue]) : false;
        if (appendCurrentValue && value) {
            setOptions([...options, value]);
        }
    }, [value]);

    useEffect(() => {
        if (!open) {
            setOptions(value ? [value] : []);
        } else if (isMinLengthMet) {
            search();
        }
    }, [open]);

    useEffect(() => {
        return () => {
            if (promiseRef.current && promiseRef.current.cancel) {
                promiseRef.current.cancel();
            }
        }
    }, []);

    const search = (query?: string) => {
        !isLoading && setIsLoading(true);
        !open && setOpen(true);
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
        }
        timeoutRef.current = setTimeout(async () => {
            try {
                setInputValue(query ?? "");
                if (promiseRef.current && promiseRef.current.cancel) {
                    promiseRef.current.cancel();
                }
                promiseRef.current = callService(query);
                const response = await promiseRef.current;
                if (!open) {
                    setIsLoading(false);
                    return;
                }
                if (!response.error && response.data && response.data.list) {
                    const appendCurrentValue = value ? !response.data.list.some((option) => value[fieldValue] === option[fieldValue]) : false;
                    if (appendCurrentValue) {
                        setOptions([...response.data.list, value]);
                    } else {
                        setOptions(response.data.list);
                    }
                }
                setIsLoading(false);
            } catch (error) {
            } finally {
            }
        }, 400);

        return () => {
            if (promiseRef.current && promiseRef.current.cancel) {
                promiseRef.current.cancel();
            }
        }
    };

    const handleOnInputChange = (event: React.SyntheticEvent<Element, Event>, value: string) => {
        setInputValue(value ?? "");
        ((minLength === (value?.length ?? 0)) || open) && search(value);
    };

    return (
        <Autocomplete
            id={"asynchronous_" + id}
            sx={{ width: "100%" }}
            disabled={disabled}
            open={open}
            onOpen={() => {
                isMinLengthMet && setOpen(true);
            }}
            onClose={() => {
                setOpen(false);
            }}
            onKeyDown={(event: {key: string}) => {
                //Select highlighted option or first option in the list if one exists. Ensure the prop is set to true to allow feature for tabbing/auto-select
                if (autoSelectFirst && event.key === 'Tab' && (options[0] || focusedOptionRef.current)) {
                    let index = focusedOptionRef.current !== null && focusedOptionRef.current !== undefined && focusedOptionRef.current > -1 ? focusedOptionRef.current : 0;
                    let val = options[index];
                    onChange(val ? val[fieldValue] : "", val === undefined ? null : val);
                    setOpen(false);
                }
            }}
            onInputChange={handleOnInputChange}
            isOptionEqualToValue={(option, value) => option[fieldValue] === value[fieldValue]}
            getOptionLabel={(option) => option[fieldDisplayText] ?? ""}
            options={options}
            value={open ? null : value}
            onChange={(e: any, val: any) => {
                //Select the option and close the dropdown list
                if (val) {
                    onChange(val[fieldValue], val);
                    setOptions([val]);
                } else {
                    onChange("", null);
                }
            }}
            loading={isLoading}
            renderInput={(params) => (
                <TextField
                    {...params}
                    {...textFieldProps}
                    name={name}
                    label={label}
                    placeholder={placeholder}
                    variant={variant}
                    helperText={helperText}
                    error={error}
                    InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                            <React.Fragment>
                                {isLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                {params.InputProps.endAdornment}
                            </React.Fragment>
                        ),
                    }}
                />
            )}
            renderOption={(props, option, { inputValue }) => {
                const matches = match(option[fieldDisplayText], inputValue, { insideWords: true });
                const parts = parse(option[fieldDisplayText], matches);

                return (
                    <li {...props} ref={observer} key={"_option_key_" + props.id  + "_" + option[fieldValue]}>
                        <div>
                            {parts.map((part:any, index:number) => (
                                <span
                                    key={index}
                                    style={{
                                        fontWeight: part.highlight ? 700 : 400,
                                    }}
                                >
                                    {part.text}
                                </span>
                            ))}
                        </div>
                    </li>
                );
            }}
        />
    );
}

export default RediAutocompleteField;

export interface AutocompleteProps {
    id: string;
    /** Field name */
    name: string;
    /** Disable field */
    disabled?: boolean;
    /** Label for the Text Input field */
    label?: string;
    /** Placeholder for the Text Input field */
    placeholder?: string;
    /** Variant for the Text Input field */
    variant?: "filled" | "outlined" | "standard";
    /** Helper text context */
    helperText?: React.ReactNode;
    /** If true, the label is displayed in an error state.  */
    error?: boolean | undefined;
    /** Actual value stored for the field */
    value: TValue | null;
    /** Call to backend to query results */
    callService: (query?: string) => HttpPromise<ListResponseDto<any>>;
    /** On change callback when dropdown field selected */
    onChange: (val: string | null, obj: any) => void;
    /** The initial value to show in the input field if matching on Id */
    initialDisplayText?: string;
    /** Value to be returned in the object array */
    fieldValue: string;
    /** Value to be displayed in the object array */
    fieldDisplayText: string;
    /** Specifies the minimum length of text before autocomplete will make suggestions */
    minLength?: number;
    /** Auto select first value when tabbing */
    autoSelectFirst?: boolean;
    /** Text field props to spread */
    textFieldProps?: TextFieldProps;
}
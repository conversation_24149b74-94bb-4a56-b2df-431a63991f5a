import { Button, MenuItem, Select, SelectChangeEvent } from "@mui/material";
import { useState } from "react";
import { GetJobCDto } from "redi-types";
import JobService from "../../services/job";
import { JobStatusCodeEnum } from "../../enum/JobStatusCoceEnum";
import { LoadingButton } from "@mui/lab";
import './styles.scss';

interface Props {
    jobData: GetJobCDto;
    onSave: (data: GetJobCDto) => void;
    onCancel?: () => void;
}

function SelectStatus(props: Props) {
    const { jobData, onSave } = props;
    const [isLoading, setIsLoading] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState<string>(jobData.statusCode);

    function cancel() {
        props.onCancel && props.onCancel();
    }

    const handleChange = (event: SelectChangeEvent) => {
        setSelectedStatus(event.target.value);
    }
    
    function handleSave() {
        JobService.Update({ ...jobData, statusCode: selectedStatus! }).then((response) => {
            if (!response.error) {
                onSave(response.data!);
            }
        })
    }

    return (
        <>
            <Select
                label="Status"
                id="status"
                value={selectedStatus}
                onChange={handleChange}
            >
                <MenuItem value={JobStatusCodeEnum.Cancelled}>Cancelled</MenuItem>
                <MenuItem value={JobStatusCodeEnum.Paid}>Paid</MenuItem>
                <MenuItem value={JobStatusCodeEnum.Active}>Active</MenuItem>
                <MenuItem value={JobStatusCodeEnum.Complete}>Complete</MenuItem>
            </Select>
            <div styleName="button-row">
                <Button variant="outlined" onClick={cancel}>
                Cancel
                </Button>
                <LoadingButton
                variant="contained"
                loading={isLoading}
                onClick={handleSave}
                >
                Save
                </LoadingButton>
            </div>
        </>
    )
}

export default SelectStatus;
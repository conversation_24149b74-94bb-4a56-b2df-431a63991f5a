export const config: Config = {
	//anything that comes before this line can be overriden by config $env files
	...process.env,
    get apiURL() {
		return process.env.REACT_APP_API_BASE_URL + "/" + this.apiPrefix;
	},
    get isDev() {
        return process.env.NODE_ENV === "development";
	},
	apiPrefix: "api/",
	tokenKey: "token-key",
	tokenExpDateKey: "tokenExpDateKey",
	defaultClaims: []
};

export default config;

interface Config {
	readonly apiURL: string;
	readonly tokenKey: string;
	readonly tokenExpDateKey: string;
    readonly defaultClaims: string[];
    readonly isDev: boolean;
	readonly apiPrefix: string;

}
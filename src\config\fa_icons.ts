/*

Add icons here:
    import { faGears } from "@fortawesome/pro-duotone-svg-icons";    
    library.add(faTimes)

Using import:
    import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

Using solid icons:
    <FontAwesomeIcon icon="times" />

Using pro light icons:
    <FontAwesomeIcon icon={['fal', 'gears']} />

Using pro regular icons:
    <FontAwesomeIcon icon={['far', 'gears']} />

Using pro thin icons:
    <FontAwesomeIcon icon={['fat', 'gears']} />

Using pro duotone icons:
    <FontAwesomeIcon icon={['fad', 'gears']} />

Setting duotone styles in CSS:
    --fa-primary-color: red;
    --fa-secondary-color: orange;
    --fa-primary-opacity: 0.8;
    --fa-secondary-opacity: 0.5
 
*/

import { IconDefinition, library } from "@fortawesome/fontawesome-svg-core";
import { faTruckTow, faFileInvoice, faBuildingUser, faSpinner } from "@fortawesome/pro-duotone-svg-icons";
import { faCalendarLines, faCog, faFileLines, faFilePen, faFolder as faFolderRegular, faUser, faUserBountyHunter, faUsers } from "@fortawesome/pro-regular-svg-icons";
import { faCheck, faCheckSquare, faEnvelope, faChevronDown, faChevronLeft, faFilePdf, faChevronRight, faCirclePlus, faClose, faCopy, faEllipsisV, faFile, faFolder, faFolderOpen, faHome, faKey, faLock, faMinusSquare, faNote, faPaste, faPenToSquare, faPlusSquare, faRotateRight, faScissors, faSquare, faTrash, faTriangleExclamation, faUserGear, faUserLock, faUserPen, faUserUnlock } from "@fortawesome/pro-solid-svg-icons";

const exportedIcons: IconDefinition[] = [
    // Add any icons that are used in exported components here (ie. SideMenu)
    faUser,
    faFilePen,
    faFileLines,
    faCalendarLines,
    faFolderRegular,
    faUserBountyHunter,
    faCog,
    faClose,
    faHome,
    faUsers,
    faChevronLeft,
    faChevronRight,
    faRotateRight,
    faCirclePlus,
    faScissors,
    faCopy,
    faPaste,
    // Solid
    faEllipsisV,
    faCheckSquare,
    faSquare,
    faChevronDown,
    faPlusSquare,
    faMinusSquare,
    faFolder,
    faFolderOpen,
    faFile,
    faUserGear,
    faTriangleExclamation,
    faKey,
    faUserLock,
    faUserUnlock,
    faLock,
    faNote,
    faTrash,
    faPenToSquare,
    faCheck,
    faUserPen,
    faFilePdf,
    faEnvelope,
    // duotone
    faTruckTow,
    faFileInvoice,
    faBuildingUser,
    faSpinner
];

library.add(
    ...exportedIcons,
    /* Local icons (demo) */
    // Regular

);

export default exportedIcons; // Export to Container Micro UI
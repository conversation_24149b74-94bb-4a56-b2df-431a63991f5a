import { createBrowserRouter } from "react-router-dom";
import withAsyncLoad from "../components/HOC/withAsyncLoad";
import routes from "../config/routes";
import ErrorPage from "../pages/Error/Error";
import Shell from "../startup/Shell";

/* Load local application pages here */
const Demo = withAsyncLoad(() => import("../pages/Demo/Demo"));

/*
 * React-Router Tutorial: https://reactrouter.com/en/main/start/tutorial
 */

const pageRoutes = [
  /* Add local application page routes here (Not exported) */
  // ie.
  {
    path: "",
    name: "Home",
    element: <Demo />,
    icon: ["far", "home"]
  },
  ...routes,
]

const router = createBrowserRouter([
  {
    path: "*",
    element: <Shell routes={pageRoutes}/>,
    errorElement: <ErrorPage />,
    children: routes
  }
], {});

export default router;

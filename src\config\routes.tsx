import withAsyncLoad from '../components/HOC/withAsyncLoad';

const PaySlipList = withAsyncLoad(() => import("../pages/PaySlipList/PaySlipList"));
const JobManagement = withAsyncLoad(() => import("../pages/JobManagement/JobManagement"));
const Job = withAsyncLoad(() => import("../pages/JobManagement/Job/Job"));
const PayRunHistory = withAsyncLoad(() => import("../pages/PayRunHistory/PayRunHistory"));

/* Routes in this file are exported to the host-container */

const routes = [
  {
    path: "JobManagement",
    name: "Job Management",
    element: <JobManagement />,
    icon: ["fad", "fa-truck-tow"]
  },
  {
    path: "JobManagement/:id",
    name: "Job",
    element: <Job />,
    excludeFromMenu: true
  },
  {
    path: "PaySlipList",
    name: "Pay Slip List",
    element: <PaySlipList />,
    excludeFromMenu: true
  },
  
  {
    path: "PayRunHistory",
    name: "Pay Run History",
    element: <PayRunHistory />,
    icon: ["fad", "fa-file-invoice"]
  },
  {
    path: "PayRunHistory/:id",
    name: "PayRunHistory",
    element: <PaySlipList />,
    excludeFromMenu: true
  }
];

export default routes;
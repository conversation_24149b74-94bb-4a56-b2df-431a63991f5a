// Main Material UI 5 (MUI) theme
import { createTheme } from "@mui/material";
import styles from './vars.scss';

/*
 * This is how to style MUI components throughout the whole application 💣!
 * The styles below are just an example and were not intended to be used
 * 
 * https://mui.com/material-ui/customization/theming/
 * https://mui.com/material-ui/customization/theme-components/
 * 
 * The styles object contains the scss variables from theme.scss & vars.scss that are being exported via webpack
*/

export const mainTheme = createTheme({
  palette: {
    primary: {
      main: styles.primaryColor
    },
    secondary: {
      main: styles.secondaryColor
    },
    // success: {
    //   main: green[500],   // '#4caf50'
    // },
    // error: {
    //   main: red[500],     // '#f44336'
    // },
    // warning: {
    //   main: orange[500],  // '#ff9800'
    // }
  },
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(','),
  },
  components: {
    /* Input */
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          backgroundColor: styles.white
        }
      }
    },
    /* Table */
    MuiTableCell: {
      styleOverrides: {
        head: {
          color: styles.headerLight,
        }
      }
    },
    MuiTableSortLabel: {
      styleOverrides: {
        root: {
          color: styles.headerLight,
          '&:hover': {
            color: styles.headerDark
          },
          '&.Mui-active': {
            color: styles.headerDark
          }
        }
      }
    }
  }
});
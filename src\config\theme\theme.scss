$primaryColor: #FF8115;
$secondaryColor: #b65c0d;
$offColor: #FF8115;
$secondaryOffColor: #ff006d;
$shadowColor: rgba(0, 0, 0, 0.1);

$pageBgColor: white;
$primaryBgColor: rgba($primaryColor, 0.06);

$grey: #395161;
$black: rgb(0, 0, 0, 0.87);
$red: #f44336;
$blue: #2196f3;
$green: #4caf50;
$yellow: #ffeb3b;
$orange: #FF8115;
$purple: #9c27b0;
$white: #fff;

$font: '-apple-system';
$fontColor: #707070;
$fontSize: 12px;
$letterSpacing: 0.8px;

// Tables
$headerLight: rgba(0, 0, 0, 0.65);
$headerDark: rgba(0, 0, 0, 0.95);

// SideMenu / TopBar
$nav-background-color: #eeeeee;
$nav-header-background-color: #FF8115;
$nav-header-text-color: #525151;
$nav-label-color: #515251;
$nav-label-color-active: #525151;
$nav-icon-color: #525151;
$nav-icon-color-active: #ffffff;

// export scss variables to TypeScript via Webpack
:export {
  primaryColor: $primaryColor;
  offColor: $offColor;
  secondaryColor: $secondaryColor;
  shadowColor: $shadowColor;
  secondaryOffColor: $secondaryOffColor;

  pageBgColor: $pageBgColor;
  primaryBgColor: $primaryBgColor;

  grey: $grey;
  black: $black;
  red: $red;
  blue: $blue;
  green: $green;
  yellow: $yellow;
  orange: $orange;
  purple: $purple;
  white: $white;
  headerLight: $headerLight;
  headerDark: $headerDark;

  font: $font;
  fontSize: $fontSize;
  letterSpacing: $letterSpacing;
  fontColor: $fontColor;

  headerTextColor: $nav-header-text-color;
  navBackgroundColor: $nav-background-color;
}
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { LoadingButton } from "@mui/lab";
import { AlertColor, Button, IconButton, InputAdornment, TextField } from "@mui/material";
import { Field, FieldProps, Formik, FormikHelpers, FormikProps } from "formik";
import { useState } from "react";

import { GetJobCDto } from "redi-types";
import * as yup from "yup";
import './styles.scss';
import JobService from "../../../services/job";
import { DatePicker } from "@mui/x-date-pickers";

import RediAutocompleteParty from 'crmcomponents/RediAutocompleteParty';

interface Props {
  id?: string;
  showHeader?: boolean;
  initialValues: GetJobCDto;
  onCancel?: () => void;
  onSave?: (message: string, severity: AlertColor, refreshData?: boolean) => void;
}

function JobForm(props: Props) {

  const baseValidation = {
    jobDate: yup.date().required('Job date is required'),
    invoiceNumber: yup.string(),
    insuranceCompanyId: yup.string(),
    insuranceCompanyName: yup.string(),
    carRegistration: yup.string(),
    yard: yup.string(),
    statusCode: yup.string().required('Status code is required'),
    spotters: yup.number(),
    contractorId: yup.string(),
    contractorName: yup.string(),
    jobTotalIncGst: yup.number().required('Job total including GST is required'),
    contractorPayIncGst: yup.number().required('Driver pay including GST is required'),
    paySlipId: yup.string(),
    towType: yup.string().required('Tow type is required'),
    customerName: yup.string().required('Customer name is required'),
    customerPhone: yup.string().required('Customer phone is required'),
    claimNumber: yup.string().required('Claim number is required'),

  };

  let schema = yup.object(baseValidation);

  const [isLoading, setIsLoading] = useState(false);

  function cancel(form: FormikProps<GetJobCDto>) {
    form.resetForm();
    props.onCancel && props.onCancel();
  }

  async function save(data: GetJobCDto, actions: FormikHelpers<any>) {
    if (props.id) {
      setIsLoading(true);

      const response = await JobService.Update(data as GetJobCDto);
      if (!response.error) {
        props.onSave && props.onSave('Successfully Updated Job', 'success', true);
      } else {
        props.onSave && props.onSave('Failed to Update Job', 'error');
      }
      setIsLoading(false);
    } else {
      const response = await JobService.Create(data as GetJobCDto);
      if (!response.error) {
        props.onSave && props.onSave('Successfully Created Job', 'success', true);
      } else {
        props.onSave && props.onSave('Failed to Create Job', 'error');
      }
      setIsLoading(false);
    }
  }

  return (
    <Formik<GetJobCDto>
      enableReinitialize // Rerender when props changes (initialValues)
      validationSchema={schema}
      initialValues={props.initialValues}
      onSubmit={(data, actions) => {
        save(data, actions);
      }}
    >
      {(form) => (
        <form onSubmit={form.handleSubmit}>
          <div styleName="container">
            {
              props.showHeader &&
              <div styleName="row">
                <div styleName="header">Job Details</div>
                <div styleName="row">
                  <IconButton onClick={() => form.handleSubmit()}>
                    <FontAwesomeIcon icon="check" />
                  </IconButton>
                  <IconButton onClick={() => cancel(form)}>
                    <FontAwesomeIcon icon="close" />
                  </IconButton>
                </div>
              </div>
            }
            <div styleName="form-grid">
              <Field
                variant="standard"
                id="jobReference"
                name="jobReference"
                label="Job Reference"
                as={TextField}
                error={form.touched.jobReference && Boolean(form.errors.jobReference)}
                helperText={form.touched.jobReference && form.errors.jobReference}
              />
              <Field
                id="jobDate"
                name="jobDate">
                {(props: FieldProps) => (
                  <DatePicker
                    inputFormat="DD/MM/YYYY"
                    label="Job Date"
                    value={props.field?.value}
                    renderInput={(params) =>
                      <TextField
                        {...params}
                        name={props?.field.name}
                        onBlur={props?.field.onBlur}
                        error={props.meta.touched && Boolean(props.meta.error)}
                        helperText={props.meta.touched && props.meta.error}
                      />
                    }
                    onChange={(date) => {
                      form.setFieldValue(props.field.name, date);
                    }}
                  />
                )}
              </Field>
              <Field
                variant="standard"
                id="invoiceNumber"
                name="invoiceNumber"
                label="Invoice Number"
                as={TextField}
                error={form.touched.invoiceNumber && Boolean(form.errors.invoiceNumber)}
                helperText={form.touched.invoiceNumber && form.errors.invoiceNumber}
              />
              <Field
                id="insuranceId"
                name="insuranceId">
                {(props: FieldProps) => (
                  <RediAutocompleteParty
                    name={props.field.name}
                    label="Insurance"
                    buttonLabel="Insurance"
                    returnAddress={false}
                    initialValue={form.values.insuranceCompanyName}
                    partyId={form.values.insuranceCompanyId}
                    showAddNewButton={true}
                    partyRoleTypeCode="Insurance"
                    partyType="Organisation"
                    partyRelationshipTypeCode="Insurance"
                    error={props.meta.touched && Boolean(props.meta.error)}
                    helperText={props.meta.touched && props.meta.error}
                    onBlur={props?.field.onBlur}
                    onChange={(dto: { partyId: string, name: string }) => {
                      form.setFieldValue("InsuranceCompanyId", dto?.partyId);
                      form.setFieldValue("InsuranceCompanyName", dto?.name);
                      if (dto) {
                        //Reset touch on field
                        form.setFieldTouched("clientPartyId", false, false);
                      }
                    }}
                  />
                )}
              </Field>
              {/* <Field
                variant="standard"
                id="contractorName"
                name="contractorName"
                label="Contractor Name"
                as={TextField}
                error={form.touched.contractorName && Boolean(form.errors.contractorName)}
                helperText={form.touched.contractorName && form.errors.contractorName}
              /> */}
              <Field
                id="contractorId"
                name="contractorId">
                {(props: FieldProps) => (
                  <RediAutocompleteParty
                    name={props.field.name}
                    label="Driver"
                    buttonLabel="Driver"
                    returnAddress={false}
                    initialValue={form.values.contractorName}
                    partyId={form.values.contractorId}
                    showAddNewButton={true}
                    partyRoleTypeCode="Contractor"
                    partyType="Organisation"
                    partyRelationshipTypeCode="Employment"
                    error={props.meta.touched && Boolean(props.meta.error)}
                    helperText={props.meta.touched && props.meta.error}
                    onBlur={props?.field.onBlur}
                    onChange={(dto: { partyId: string, name: string }) => {
                      form.setFieldValue("contractorId", dto?.partyId);
                      form.setFieldValue("contractorName", dto?.name);
                      if (dto) {
                        //Reset touch on field
                        form.setFieldTouched("clientPartyId", false, false);
                      }
                    }}
                  />
                )}
              </Field>
              <Field
                variant="standard"
                id="carRegistration"
                name="carRegistration"
                label="Rego"
                as={TextField}
                error={form.touched.carRegistration && Boolean(form.errors.carRegistration)}
                helperText={form.touched.carRegistration && form.errors.carRegistration}
              />
              <Field
                variant="standard"
                id="yard"
                name="yard"
                label="Yard"
                as={TextField}
                error={form.touched.yard && Boolean(form.errors.yard)}
                helperText={form.touched.yard && form.errors.yard}
              />
              
              <Field
                type="number"
                variant="standard"
                id="spotters"
                name="spotters"
                label="Spotter's Fee"
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
                as={TextField}
                error={form.touched.spotters && Boolean(form.errors.spotters)}
                helperText={form.touched.spotters && form.errors.spotters}
              />
              <Field
                variant="standard"
                id="jobTotalIncGst"
                name="jobTotalIncGst"
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}

                label="Towing Fee (incl. GST)"
                as={TextField}
                error={form.touched.jobTotalIncGst && Boolean(form.errors.jobTotalIncGst)}
                helperText={form.touched.jobTotalIncGst && form.errors.jobTotalIncGst}
              />
              {/* 
              <Field
                variant="standard"
                id="contractorPayIncGst"
                name="contractorPayIncGst"
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}

                label="Contractor Pay Inc GST"
                as={TextField}
                error={form.touched.contractorPayIncGst && Boolean(form.errors.contractorPayIncGst)}
                helperText={form.touched.contractorPayIncGst && form.errors.contractorPayIncGst}
              /> */}

            </div>
            {
              !props.showHeader &&
              <div styleName="button-row">
                <Button variant="outlined" onClick={() => cancel(form)}>
                  Cancel
                </Button>
                <LoadingButton
                  variant="contained"
                  loading={isLoading}
                  onClick={() => form.handleSubmit()}
                >
                  Save
                </LoadingButton>
              </div>
            }
          </div>
        </form>
      )}
    </Formik>
  );
}

export default JobForm;
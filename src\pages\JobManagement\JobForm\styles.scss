@import "../../../config/theme/vars.scss";

.row {
  display: flex;
  align-items: center;
}

.container {
  display: flex;
  flex-direction: column;

  >.row {
    justify-content: space-between;
    margin-bottom: 1.25rem;

    .header {
      color: $primaryColor;
      font-size: 1.3rem;
      font-weight: 600;
    }

    button {
      color: $primaryColor;

      .menu-icon {
        height: 1.25rem;
        width: 1.25rem;
        font-size: 1.25rem;
        padding: 0;
        color: rgba(0, 0, 0, 0.6);
      }
    }

    svg {        
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
    }    
  }

  .grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;

    >.column {
      color: #4E4949;
      font-size: 1rem;

      .label {
        font-weight: 600;
      }

      .value {
        font-weight: 400;
      }

      .green {
        color: $green !important;
      }
      
      .red {
        color: $red !important;
      }
    }
  }
}

// Edit
.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0 2rem;
  padding-top: 1rem;

  // Stop form field's validation growing/shinking the dialog
  & > div {
    min-height: 72px;
  }

  & > label {
    margin: 0 0 1.8rem 0;
  }
}

.button-row {
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}
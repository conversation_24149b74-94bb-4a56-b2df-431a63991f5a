import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Alert, AlertColor, Button, Checkbox, Drawer, IconButton, ListItemText, Menu, MenuItem, Select, SelectChangeEvent, Snackbar, TableCell } from "@mui/material";
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { GetJobCDto, GetListJobCDto } from "redi-types";
import DataTable, { TableHeader } from "../../components/DataTable/DataTable";
import DraggableDialog from "../../components/DraggableDialog/DraggableDialog";
import YesNoDialog from "../../components/YesNoDialog/YesNoDialog";
import { dateFormatOptions } from "../../utils/dateFormOptions";
import { NIL as NIL_UUID, v4 as uuidv4 } from 'uuid';

import './styles.scss';
import JobForm from './JobForm/JobForm';
import JobService from '../../services/job';
import JobActionButtons from './Job/JobDetails/JobActionButtons';
import { JobStatusCodeEnum } from '../../enum/JobStatusCoceEnum';
import { LoadingButton } from '@mui/lab';
import SelectStatus from '../../components/SelectStatus/SelectStatus';
import { showToast } from 'redi-formik-material';

interface Props { }

const initialValues: GetJobCDto = {
    jobId: NIL_UUID,
    jobReference: '',
    jobDate: new Date(),
    invoiceNumber: '',
    insuranceCompanyId: '',
    insuranceCompanyName: '',
    carRegistration: '',
    yard: '',
    statusCode: 'Active',
    spotters: 0,
    contractorId: '',
    contractorName: '',
    jobTotalIncGst: 0,
    contractorPayIncGst: 0,
    paySlipId: '',
    fields: {
        towType: '',
        allocatedBy: '',
        customerName: '',
        customerPhone: '',
        claimNumber: '',
        jobNumber: '',
        jobDate: '',
        yard: '', //Current Yard
        yardLocationId: '',
        invoiceNumber: '',
        transferDate: '',
        transferLocation: '',
        insurer: '',
        truckId: '',
        truckListingId: ''
    }
};


function JobManagement(props: Props) {

    const navigate = useNavigate();

    const [isLoading, setIsLoading] = useState(false);
    const [refreshTableTrigger, setRefreshTableTrigger] = useState(0);

    const [clickedJob, setClickedJob] = useState<GetJobCDto>();


    // Dialog
    const [isAddJobDialogOpen, setIsAddJobDialogOpen] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [isEditJobDialogOpen, setIsEditJobDialogOpen] = useState(false);
    const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);

    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

    // Snackbar
    const [showSnackbar, setShowSnackbar] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');
    const [snackbarSeverity, setSnackbarSeverity] = useState<AlertColor | undefined>();

    // currnency format regex
    function currencyFormat(num: number) {
        return '$' + num.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
    }
    //Used for multi updates
    const [selectedList, setSelectedList] = useState<GetJobCDto[]>([]);
    const [isCloseQuoteDialogOpen, setIsCloseQuoteDialogOpen] = useState(false);

    // Required fields + fields from add form

    async function setStatus(status: JobStatusCodeEnum) {
        await JobService.UpdateListStatus(selectedList, status);
        setRefreshTableTrigger(trigger => trigger + 1);
        setSelectedList([]);

    }

    const tableHeaders: TableHeader[] = [
        { id: 'jobDate', label: 'Job Date', isSortable: true },
        { id: 'jobReference', label: 'Job Reference', isSortable: true },
        { id: 'invoiceNumber', label: 'Invoice Number', isSortable: true },
        { id: 'contractorName', label: 'Driver', isSortable: true },
        { id: 'insuranceCompanyName', label: 'Insurance', isSortable: true },
        { id: 'carRegistration', label: 'Rego', isSortable: true },
        { id: 'yard', label: 'Yard', isSortable: true },
        { id: 'spotters', label: "Spotter's Fee", isSortable: true, align: "right" },
        { id: 'jobTotalIncGst', label: 'Towing Fee \n (incl. GST)', isSortable: true, align: "right" },
        { id: 'statusCode', label: 'Status Code', isSortable: true },
        { id: 'checkbox', label: '' }, // Blank for row menu button
        { id: 'menu', label: '' } // Blank for row menu button
    ];


    function addSelectedList(job: GetJobCDto, val: boolean) {
        let newList = [...selectedList];
        if (val) {
            newList.push(job);
            setSelectedList(newList);
        }
        else {
            newList = newList.filter(listItem => listItem != job);
            setSelectedList(newList);
        }

    }


    function renderTableRow(data: GetListJobCDto) {
        return (
            <>
                {/* Primary column should be bolded (Does not always mean the far left column) */}
                <TableCell>
                    {
                        data?.jobDate
                            ? new Date(data.jobDate).toLocaleDateString('en-AU')
                            : 'N/A'
                    }
                </TableCell>
                <TableCell>
                    <div>{data.jobReference}</div>
                </TableCell>
                <TableCell>{data.invoiceNumber}</TableCell>
                <TableCell>{data.contractorName}</TableCell>
                <TableCell>{data.insuranceCompanyName}</TableCell>
                <TableCell>{data.carRegistration}</TableCell>
                <TableCell>{data.yard}</TableCell>
                <TableCell align="right">{currencyFormat(data.spotters as number)}</TableCell>
                <TableCell align="right">{currencyFormat(data.jobTotalIncGst)}</TableCell>
                <TableCell>{data.statusCode}</TableCell>
                <TableCell>
                    <Checkbox
                        checked={selectedList.includes(data)}
                        onChange={(val) => addSelectedList(data, val.target.checked)}
                        onClick={(event) => {
                            event.stopPropagation();
                        }}
                        color="primary" />
                </TableCell>
                <TableCell align="right">
                    <RowMenu rowData={data} />
                </TableCell>
            </>
        );
    };

    /* Add Job Dialog */

    async function onAddJobDialogSave(message: string, severity: AlertColor) {
        setIsAddJobDialogOpen(false);
        setShowSnackbar(true);
        setSnackbarMessage(message);
        setSnackbarSeverity(severity);
        setRefreshTableTrigger(trigger => trigger + 1);

    }


    async function onEditJobDialogSave(message: string, severity: AlertColor) {
        setIsEditJobDialogOpen(false);
        setShowSnackbar(true);
        setSnackbarMessage(message);
        setSnackbarSeverity(severity);
        setRefreshTableTrigger(trigger => trigger + 1);

    }
    async function onEditStatusSave() {
        setIsStatusDialogOpen(false);
        showToast("success", "Test")
        setRefreshTableTrigger(trigger => trigger + 1);

    }
    /* Edit Note Dialog */

    // function closeEditNoteDialog() {
    //     setIsEditNoteDialogOpen(false);
    //     setClickedUser(undefined);
    // }

    // async function onEditNoteDialogSave(message: string, severity: AlertColor) {
    //     closeEditNoteDialog();
    //     setShowSnackbar(true);
    //     setSnackbarMessage(message);
    //     setSnackbarSeverity(severity);
    //     // Refresh table data
    //     setRefreshTableTrigger(trigger => trigger + 1);
    // }

    /* Delete Job Dialog */

    function closeDeleteDialog() {
        setIsDeleteDialogOpen(false);
    }

    async function deleteJob() {
        closeDeleteDialog();
        if (clickedJob) {
            const response = await JobService.Delete(clickedJob.jobId);
            if (!response.error) {
                setShowSnackbar(true);
                setSnackbarMessage('Successfully deleted user');
                setSnackbarSeverity('success');
                // Refresh table data
                setRefreshTableTrigger(trigger => trigger + 1);
            } else {
                setShowSnackbar(true);
                setSnackbarMessage('Failed to delete user');
                setSnackbarSeverity('error');
            }
        }
        setClickedJob(undefined);
    }

    const Dialogs = () => {
        return (
            <>
                {/* Add New Job Dialog */}
                <DraggableDialog title="Add New Job" isOpen={isAddJobDialogOpen} onCancel={() => setIsAddJobDialogOpen(false)}>
                    <JobForm
                        initialValues={initialValues}
                        onCancel={() => setIsAddJobDialogOpen(false)}
                        onSave={onAddJobDialogSave}
                    />
                </DraggableDialog>
                <DraggableDialog title="Edit Job" isOpen={isEditJobDialogOpen} onCancel={() => setIsEditJobDialogOpen(false)}>
                    <JobForm
                        id={clickedJob?.jobId}
                        initialValues={clickedJob!}
                        onCancel={() => setIsEditJobDialogOpen(false)}
                        onSave={onEditJobDialogSave}
                    />
                </DraggableDialog>
                {/* Delete User Dialog */}
                <YesNoDialog
                    title="Delete Job"
                    bodyText="Are you sure? This cannot be reversed"
                    isOpen={isDeleteDialogOpen}
                    onNo={closeDeleteDialog}
                    onYes={deleteJob}
                />

                <DraggableDialog title="Edit Status" isOpen={isStatusDialogOpen} onCancel={() => setIsStatusDialogOpen(false)}>
                    <SelectStatus
                        jobData={clickedJob!}
                        onSave={onEditStatusSave}
                        onCancel={() => setIsStatusDialogOpen(false)}
                    />
                </DraggableDialog>
            </>
        );
    }

    const RowMenu = (props: { rowData: GetJobCDto }) => {
        const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
        const isOpen = Boolean(anchorEl);
        const openMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
            event.stopPropagation();
            setAnchorEl(event.currentTarget);
        };
        const handleClose = (event: React.MouseEvent<HTMLButtonElement>) => {
            event.stopPropagation();
            setAnchorEl(null);
        };
        const handleEdit = (event: React.MouseEvent) => {
            event.stopPropagation();
            setAnchorEl(null);
            setClickedJob(props.rowData);
            setIsEditJobDialogOpen(true);
        };
        const handleDelete = (event: React.MouseEvent) => {
            event.stopPropagation();
            setAnchorEl(null);
            setClickedJob(props.rowData);
            setIsDeleteDialogOpen(true);
        };
        const handleStatus = (event: React.MouseEvent) => {
            event.stopPropagation();
            setAnchorEl(null);
            setClickedJob(props.rowData);
            setIsStatusDialogOpen(true);
        };

        return (
            <>
                <IconButton
                    id="actions-list"
                    aria-controls={isOpen ? 'basic-menu' : undefined}
                    aria-haspopup="true"
                    aria-expanded={isOpen ? 'true' : undefined}
                    onClick={openMenu}
                >
                    <FontAwesomeIcon styleName="row-menu-icon" icon="ellipsis-v" />
                </IconButton>
                <Menu
                    id="actions-menu"
                    anchorEl={anchorEl}
                    open={isOpen}
                    onClose={handleClose}
                    MenuListProps={{ 'aria-labelledby': 'menu-button' }}
                >
                    <MenuItem onClick={handleEdit}>
                        <ListItemText>Edit</ListItemText>
                    </MenuItem>
                    <MenuItem onClick={handleStatus}>
                        <ListItemText>Change Status</ListItemText>
                    </MenuItem>
                    <MenuItem onClick={handleDelete}>
                        <ListItemText>Delete</ListItemText>
                    </MenuItem>
                </Menu>
            </>
        );
    }

    return (
        <div style={{ overflow: 'auto', position: 'relative' }}>
            <div styleName="card">
                <DataTable<GetListJobCDto, "jobId">
                    primaryKeyProperty="jobId"
                    title="Jobs"
                    tableId='base-jobManagement-'
                    pageSize={10}
                    initialSortColumn="JobReference"
                    refreshTableTrigger={refreshTableTrigger}
                    tableHeight={600}
                    addButtonLabel="Add Job"
                    addButtonOnClick={() => { setIsAddJobDialogOpen(true) }}
                    tableHeaders={tableHeaders}
                    renderTableRow={renderTableRow}
                    filterOptions={['All', 'Active', 'Cancelled', "Complete", "Paid"]}
                    onRowClick={(row) => { navigate(`/JobManagement/${row.jobId}`) }}
                    callService={(inListParameters, inSearch, inFilter) => {
                        const _inFilter = inFilter === 'All' ? '' : inFilter;
                        return JobService.List(inListParameters, inSearch, _inFilter)
                    }}
                />

                <Dialogs />


                <React.Fragment key={"autotow-jobList-"}>
                    <Drawer
                        anchor={"left"}
                        open={selectedList.length > 0}
                        onClose={() => setSelectedList([])}
                        hideBackdrop={true}
                        variant="persistent"
                    >
                        <div styleName="drawer-header">Bulk Selection Options</div>
                        <Button styleName="drawer-button" onClick={() => setStatus(JobStatusCodeEnum.Complete)}>
                            <div>Complete</div>
                        </Button>
                        <Button styleName="drawer-button" onClick={() => setStatus(JobStatusCodeEnum.Active)}>
                            <div>Active</div>
                        </Button>
                        <Button styleName="drawer-button" onClick={() => setStatus(JobStatusCodeEnum.Cancelled)}>
                            <div>Cancelled</div>
                        </Button>
                        <Button styleName="drawer-button" onClick={() => setStatus(JobStatusCodeEnum.Paid)}>
                            <div>Paid</div>
                        </Button>
                        <Button styleName="drawer-button" onClick={() => setSelectedList([])}>
                            <div>Unselect All</div>
                        </Button>
                    </Drawer>
                </React.Fragment>



                <Snackbar open={showSnackbar}
                    autoHideDuration={4000}
                    onClose={() => setShowSnackbar(false)}
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}>
                    <Alert onClose={() => setShowSnackbar(false)} severity={snackbarSeverity}>
                        {snackbarMessage}
                    </Alert>
                </Snackbar>
            </div>
        </div>
    );
}

export default JobManagement;
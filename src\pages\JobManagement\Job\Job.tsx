import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Alert, AlertColor, CircularProgress, IconButton, ListItemIcon, ListItemText, Menu, MenuItem, Snackbar } from "@mui/material";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { GetJobCDto, } from "redi-types";

import { dateFormatOptions } from "../../../utils/dateFormOptions";
import JobDetails from "./JobDetails/JobDetails";
import './styles.scss';
import JobService from "../../../services/job";
import withAsyncLoad from "../../../components/HOC/withAsyncLoad";

const ContractorDetails = withAsyncLoad<{ id: string }>(() => import('crmcomponents/ContractorExport'));


interface Props { }

function Job(props: Props) {

  const { id } = useParams();   // Url Params

  const [tabIndex, setTabIndex] = useState(0);

  // User Details
  const [job, setJob] = useState<GetJobCDto>();

  const [isLoading, setIsLoading] = useState(true);

  // Snackbar
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<AlertColor | undefined>();

  const [hasTab2BeenClicked, setHasTab2BeenClicked] = useState(false);

  useEffect(() => {
    // Get Data from API
    (async () => {
      await getJobDetails();

      setIsLoading(false);
    })();
  }, []);

  async function getJobDetails() {
    if (id) {
      const getJobDetails = await JobService.Get(id);
      if (!getJobDetails.error && getJobDetails.data) {
        setJob(getJobDetails?.data);
      }
    }
  }

  async function updateSnackbar(message: string, severity: AlertColor, refreshData: boolean = false) {
    setShowSnackbar(true);
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    if (refreshData) {
      await getJobDetails();
    }
  }

  return (
    <div styleName="page">
      {
        isLoading
          ?
          <div styleName="loading">
            <CircularProgress color="primary" size={50} />
          </div>
          :
          <>
            <div styleName="container">
              {/* Note: Having this section in its own component will cause <UserAudit/> to remount on tab change */}
              <div styleName="content">
                <div styleName="summary-bar">
                  <div styleName="column">
                    <div styleName="top">{job?.jobReference}</div>
                    <div styleName="bottom">Job Ref</div>
                  </div>
                  <div styleName="column">
                    <div styleName="top">
                      {
                        job?.jobDate
                        ? new Date(job.jobDate).toLocaleDateString('en-AU')
                        : 'N/A'
                      }
                      <div styleName="bottom">Job Date</div>
                    </div>
                  </div>
                  <div styleName="column">
                    <div styleName="top">
                      {
                        job?.createdOn 
                        ? new Date(job.createdOn).toLocaleDateString('en-AU')
                        : 'N/A'
                      }
                    </div>
                    <div styleName="bottom">Job Created</div>
                  </div>
                  <div styleName="column">
                    <div styleName="top">{job?.carRegistration ?? 'N/A'}</div>
                    <div styleName="bottom">Rego</div>
                  </div>
                  <div styleName="column">
                    <div styleName="top">{job?.statusCode ?? 'N/A'}</div>
                    <div styleName="bottom">Status</div>
                  </div>
                  <div styleName="column">
                    <div styleName="top">{job?.yard}</div>
                    <div styleName="bottom">Yard</div>
                  </div>
                </div>

                <div styleName="main-content">
                  <div styleName="row">
                    <div styleName={`tab ${tabIndex === 0 ? 'active' : ''}`} onClick={() => setTabIndex(0)}>
                        Job
                    </div>
                  </div>
                  <div styleName="tab-body">
                    <div styleName="tab-grid-1" style={{ display: tabIndex === 0 ? 'grid' : 'none' }}>
                      <div>
                        <JobDetails
                          initialValues={job as GetJobCDto}
                          onSave={updateSnackbar}
                        />
                      </div>
                      <div>
                        {job && job.contractorId ? <ContractorDetails id={job.contractorId} /> : <div>Driver not found</div>}
                      </div>

                    </div>
                  </div>
                </div>
              </div>
            </div>
            <Snackbar open={showSnackbar}
              autoHideDuration={4000}
              onClose={() => setShowSnackbar(false)}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}>
              <Alert onClose={() => setShowSnackbar(false)} severity={snackbarSeverity}>
                {snackbarMessage}
              </Alert>
            </Snackbar>
          </>
      }
    </div>
  )
}

export default Job;
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { IconButton, ListItemText, Menu, MenuItem } from "@mui/material";
import { useState } from "react";
import { GetJobCDto } from "redi-types";
import './styles.scss';
import { JobStatusCodeEnum } from "../../../../enum/JobStatusCoceEnum";
import JobService from "../../../../services/job";
import YesNoDialog from "../../../../components/YesNoDialog/YesNoDialog";

function JobActionButtons(props: Props) {
    const { jobData, onSave } = props;
    const [selectedStatus, setSelectedStatus] = useState<JobStatusCodeEnum | null>();
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

    const open = Boolean(anchorEl);
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
        setSelectedStatus(null);
    };
    const handleStatusSelect = (caseStatusCode: JobStatusCodeEnum) => {
        if (jobData.statusCode === caseStatusCode) { return; }
        setSelectedStatus(caseStatusCode);

    };
    const handleSave = () => {
        JobService.Update({ ...jobData, statusCode: selectedStatus! }).then((response) => {
            if (!response.error) {
                onSave(response.data!);
            }
        }
        );
        handleClose();
    };

    return (
        <>
            <IconButton
                id="actions-list"
                aria-controls={open ? 'case-details-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={open ? 'true' : undefined}
                onClick={handleClick}
            >
                <FontAwesomeIcon styleName="menu-icon" icon="ellipsis-v" />
            </IconButton>
            <Menu
                id="actions-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{ "aria-labelledby": "menu-button" }}
            >
                <MenuItem onClick={() => handleStatusSelect(JobStatusCodeEnum.Cancelled)}>
                    <ListItemText>Cancelled</ListItemText>
                </MenuItem>
                <MenuItem onClick={() => handleStatusSelect(JobStatusCodeEnum.Paid)}>
                    <ListItemText>Paid</ListItemText>
                </MenuItem>
                <MenuItem onClick={() => handleStatusSelect(JobStatusCodeEnum.Active)}>
                    <ListItemText>Active</ListItemText>
                </MenuItem>
                <MenuItem onClick={() => handleStatusSelect(JobStatusCodeEnum.Complete)}>
                    <ListItemText>Complete</ListItemText>
                </MenuItem>

            </Menu>
            {selectedStatus ?
                <YesNoDialog
                    title="Change Status"
                    bodyText={`Please confirm your are changing status from ${jobData.statusCode} to ${JobStatusCodeEnum[selectedStatus]}.`}
                    isOpen={true}
                    onNo={handleClose}
                    onYes={handleSave}
                /> :
                null}
        </>
    )
}

export default JobActionButtons;

interface Props {
    jobData: GetJobCDto;
    onSave: (data: GetJobCDto) => void;
}
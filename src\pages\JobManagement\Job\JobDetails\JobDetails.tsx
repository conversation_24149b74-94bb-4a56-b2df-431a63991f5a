import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { AlertColor, IconButton, ListItemIcon, ListItemText, Menu, MenuItem } from "@mui/material";
import { useState } from "react";
import { ExtendedApplicationUserDto, GetJobCDto, GetListClaimCDto, GetListRoleCDto } from "redi-types";
import DraggableDialog from "../../../../components/DraggableDialog/DraggableDialog";
import YesNoDialog from "../../../../components/YesNoDialog/YesNoDialog";
import './styles.scss';
import JobForm from "../../JobForm/JobForm";
import JobService from "../../../../services/job";
import { dateFormatOptions } from "../../../../utils/dateFormOptions";
import JobActionButtons from "./JobActionButtons";

interface Props {
  initialValues: GetJobCDto;
  onSave?: (message: string, severity: AlertColor, refreshData?: boolean) => void;
}

function JobDetails(props: Props) {

  const [isEdit, setIsEdit] = useState(false);
  // Dialogs
  const [isResetPasswordDialogOpen, setIsResetPasswordDialogOpen] = useState(false);
  const [isEditNoteDialogOpen, setIsEditNoteDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // currnency format regex
  function currencyFormat(num: number) {
    return '$' + num.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
  }
  
  /* Edit Job Dialog */

  async function save(message: string, severity: AlertColor, refreshData: boolean = false) {
    setIsEdit(false);
    props.onSave && props.onSave(message, severity, refreshData);
  }


  /* Delete Job Dialog */

  async function deleteJob() {
    setIsDeleteDialogOpen(false);
    const response = await JobService.Delete(props.initialValues.jobId);
    if (!response.error) {
      props.onSave && props.onSave('Successfully deleted user', 'success', true);
    } else {
      props.onSave && props.onSave('Failed to delete user', 'error');
    }
  }

  function closeDeleteDialog() {
    setIsDeleteDialogOpen(false);
  }



  const Dialogs = () => {
    return (
      <>
        <YesNoDialog
          title="Delete Job"
          bodyText="Are you sure? This cannot be reversed"
          isOpen={isDeleteDialogOpen}
          onNo={closeDeleteDialog}
          onYes={deleteJob}
        />
      </>
    );
  }
  function renderView() {
    const job = props.initialValues;

    if (!job) {
      return null;
    }

    return (
      <div styleName="container">

        <div styleName="row">
          <div styleName="header">Job Details</div>
          <div styleName="row">
            <IconButton onClick={() => setIsEdit((prev) => !prev)}>
              <FontAwesomeIcon icon="pen-to-square" />
            </IconButton>
            <JobActionButtons
              jobData={job}
              onSave={(data) => props.onSave && props.onSave('Status updated', 'success', true)}
            />
          </div>
        </div>

        <div styleName="align-items-start">

          <div styleName="grid mr-15">
            <div styleName="column">
              <div styleName="label">Job Reference</div>
              <div styleName="value">{job.jobReference}</div>
            </div>

            <div styleName="column">
              <div styleName="label">Invoice Number</div>
              <div styleName="value uppercase">{job.invoiceNumber}</div>
            </div>

            <div styleName="column">
              <div styleName="label">Job Date</div>
              <div styleName="value">{job?.createdOn
                ? new Date(job.createdOn).toLocaleDateString('en-AU')
                : 'N/A'}</div>
            </div>
            <div styleName="column">
              <div styleName="label">Insurance</div>
              <div styleName="value">{job.insuranceCompanyName}</div>
            </div>
            <div styleName="column">
              <div styleName="label">Status</div>
              <div styleName="value">{job.statusCode}</div>
            </div>
          </div>

          <div styleName="grid">
            <div styleName="column">
              <div styleName="label">Rego</div>
              <div styleName="value uppercase">{job.carRegistration}</div>
            </div>

            <div styleName="column">
              <div styleName="label">Yard</div>
              <div styleName="value">{job.yard}</div>
            </div>

            <div styleName="column">
              <div styleName="label">Spotter's Fees</div>
              <div styleName="value">{currencyFormat(job.spotters!)}</div>
            </div>

            <div styleName="column">
              <div styleName="label">Towing Fee (Incl. GST)</div>
              <div styleName="value">{currencyFormat(job.jobTotalIncGst)}</div>
            </div>
            <div styleName="column">
              <div styleName="label">RCTI (Incl. GST)</div>
              <div styleName="value">{currencyFormat(job.contractorPayIncGst)}</div>
            </div>

          </div>
        </div>
        <div>
          <div styleName="footer">{`Job Created by ${job.createdByName}, on `}
          </div>
          <div styleName="footer">
            {job?.createdOn
              ? new Date(job.createdOn).toLocaleDateString('en-AU', dateFormatOptions)
              : 'N/A'}
          </div>
        </div>
      </div>
    )
  }

  function renderEdit() {
    return (
      <JobForm
        showHeader
        id={props.initialValues.jobId}
        initialValues={props.initialValues}
        onCancel={() => setIsEdit(false)}
        onSave={save}
      />
    );
  }

  return (
    <>
      {
        !props.initialValues
          ?
          <div>Error: User data failed to load</div>
          :
          isEdit
            ?
            renderEdit()
            :
            renderView()
      }
      <Dialogs />
    </>
  );
}

export default JobDetails;

enum DropdownMenuItems {
  DeleteJob = 'deleteJob'
}
@import "../../config/theme/vars.scss";

.card {
  background: $white;
  margin: 2rem 2rem 0 2rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  white-space: break-spaces;
}

.green {
  color: $green !important;
}

.red {
  color: $red !important;
}

.row-menu-icon {
  height: 20px;
  width: 20px;
  padding: 0;
  color: rgba(0, 0, 0, 0.6);
}

.clickable {
  cursor: pointer;
}

.button-row {
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}

.drawer-button {
  color: white !important;
  background-color: $primaryColor !important;
  margin: 10px !important;
}

.drawer-header {
  color: white !important;
  margin: 10px;
  font-size: 18px;
}
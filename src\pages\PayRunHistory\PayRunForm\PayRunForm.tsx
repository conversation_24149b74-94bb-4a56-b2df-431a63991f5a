import { AlertColor, <PERSON>ton, Grid, TextField } from "@mui/material";
import './styles.scss';
import { useState } from "react";
import PayRunService from "../../../services/payRun";
import { LoadingButton } from "@mui/lab";

interface Props {
  onCancel?: () => void;
  onSave?: (message: string, severity: AlertColor, refreshData?: boolean) => void;
}

function PayRunForm(props: Props) {
  const [fromDate, setFromDate] = useState<Date | null>(null);
  const [toDate, setToDate] = useState<Date | null>(null);
  const [loading, setLoading] = useState(false);

  const handleFromDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedDate = new Date(event.target.value);
    selectedDate.setHours(0, 0, 0, 0);
    setFromDate(selectedDate);
  };

  const handleToDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedDate = new Date(event.target.value);
    selectedDate.setHours(23, 59, 59, 999);
    setToDate(selectedDate);
  };

  const handleCreatePayrun = async () => {
    if (toDate == null || fromDate == null) {
      props.onSave && props.onSave('Must enter a date in both fields', 'error');
      return;
    }
    if (toDate < fromDate) {
      props.onSave && props.onSave('To date must be after the from date', 'error');
      return;
    }
    const currentDate = new Date();
    currentDate.setHours(23, 59, 59, 999);

    if (toDate > currentDate) {
      props.onSave && props.onSave('To date cant be in the future', 'error');
      return;
    }
    const fromUTCDate = convertLocalDateToUTCDate(fromDate!);
    const toUTCDate = convertLocalDateToUTCDate(toDate!);

    setLoading(true);
    const response = await PayRunService.CreatePayRun(fromUTCDate, toUTCDate);
    if (!response.error) {
      props.onSave && props.onSave('Successfully Created PayRun', 'success', true);
    } else {
      if (response.error.response.message.includes('No Completed jobs')) {
        props.onSave && props.onSave('No Completed Jobs within the dates selected', 'error');
      } else {
        props.onSave && props.onSave('Failed to Create PayRun: ' + response.error.response.message, 'error');
      }
      console.log(response.error.response.message);
    }
    setLoading(false);
  }

  function convertLocalDateToUTCDate(date: Date) {
    var newDate = new Date(date.getTime() + date.getTimezoneOffset() * 60 * 1000);
    return newDate;
  }
  function formatDateToInputValue(date: Date): string {
    const year = date.getFullYear();
    const month = `${(date.getMonth() + 1)}`.padStart(2, '0');
    const day = `${date.getDate()}`.padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
  return (
    <>
      <Grid marginBottom={'15px'} container direction="column" spacing={2}>
        <Grid item xs={4} sm={4}>
          <TextField
            fullWidth
            required
            id="from-date"
            label="From Date"
            type="date"
            value={fromDate ? formatDateToInputValue(fromDate) : ''}
            onChange={handleFromDateChange}
            InputLabelProps={{
              shrink: true,
            }}
          />
        </Grid>
        <Grid item xs={4} sm={4}>
          <TextField
            fullWidth
            id="to-date"
            required
            label="To Date"
            type="date"
            value={toDate ? formatDateToInputValue(toDate) : ''}
            onChange={handleToDateChange}
            InputLabelProps={{
              shrink: true,
            }}
          />
        </Grid>
      </Grid>
      <LoadingButton loading={loading} variant="contained" color="primary" onClick={handleCreatePayrun}>
        Create Payrun
      </LoadingButton>
    </>
  );
}



export default PayRunForm;

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Alert, AlertColor, IconButton, ListItemText, Menu, MenuItem, Snackbar, TableCell } from "@mui/material";
import { useState } from "react";
import { useNavigate } from 'react-router-dom';
import { GetPayRunCDto, GetListJobCDto, GetListPayRunCDto } from "redi-types";
import DataTable, { TableHeader } from "../../components/DataTable/DataTable";
import DraggableDialog from "../../components/DraggableDialog/DraggableDialog";
import YesNoDialog from "../../components/YesNoDialog/YesNoDialog";
import UserService from "../../services/demoServices/user";
import { dateFormatOptions } from "../../utils/dateFormOptions";
import { NIL as NIL_UUID, v4 as uuidv4 } from 'uuid';

import './styles.scss';
import PaySlipService from '../../services/paySlip';
import PayRunService from '../../services/payRun';
import PayRunForm from './PayRunForm/PayRunForm';


interface Props { }

const initialValues: GetPayRunCDto = {
    payRunId: NIL_UUID,
    fromDate: new Date(),
    toDate: new Date(),
    statusCode: '',
    payRunTotalIncGst: 0,
    paySlipCount: 0,
};


function PayRunHistory(props: Props) {

    const navigate = useNavigate();


    const [refreshTableTrigger, setRefreshTableTrigger] = useState(0);

    const [clickedPayRun, setClickedPayRun] = useState<GetPayRunCDto>();


    // Dialog
    const [isAddPayRunDialogOpen, setIsAddPayRunDialogOpen] = useState(false);
    const [isDeletePayRunDialogOpen, setIsDeletePayRunDialogOpen] = useState(false);
    const [isEditPayRunDialogOpen, setIsEditPayRunDialogOpen] = useState(false);

    // Snackbar
    const [showSnackbar, setShowSnackbar] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');
    const [snackbarSeverity, setSnackbarSeverity] = useState<AlertColor | undefined>();

    // currnency format regex
    function currencyFormat(num: number) {
        return '$' + num.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
    }

    const tableHeaders: TableHeader[] = [
        { id: 'fromDate', label: 'From', isSortable: true },
        { id: 'toDate', label: 'To', isSortable: true },
        { id: 'createdOn', label: 'Created on', isSortable: true },
        { id: 'statusCode', label: 'Status', isSortable: true },
        { id: 'payRunTotalIncGst', label: 'RCTI (incl. GST)', align: "right", isSortable: true },
        { id: 'paySlipCount', label: 'Payslips', isSortable: true },
        { id: 'menu', label: '' } // Blank for row menu button
    ];

    function renderTableRow(data: GetListPayRunCDto) {
        return (
            <>
                {/* Primary column should be bolded (Does not always mean the far left column) */}

                <TableCell>
                    {
                        data?.fromDate
                            ? new Date(data.fromDate).toLocaleDateString('en-AU')
                            : 'N/A'
                    }
                </TableCell>
                <TableCell>
                    {
                        data?.toDate
                            ? new Date(data.toDate).toLocaleDateString('en-AU')
                            : 'N/A'
                    }
                </TableCell>
                <TableCell>
                    {
                        data?.createdOn
                            ? new Date(data.createdOn).toLocaleString('en-AU', {
                                day: 'numeric',
                                month: 'numeric',
                                year: 'numeric',
                                hour: 'numeric',
                                minute: 'numeric'
                            })
                            : 'N/A'
                    }
                </TableCell>
                <TableCell >{data.statusCode}</TableCell>
                <TableCell align="right">{currencyFormat(data.payRunTotalIncGst)}</TableCell>
                <TableCell>{data.paySlipCount}</TableCell>
                <TableCell align="right">
                    <RowMenu rowData={data} />
                </TableCell>
            </>
        );
    };

    /* Add Job Dialog */

    async function onAddPayRunDialogSave(message: string, severity: AlertColor) {
        setIsAddPayRunDialogOpen(false);
        setShowSnackbar(true);
        setSnackbarMessage(message);
        setSnackbarSeverity(severity);
        setRefreshTableTrigger(trigger => trigger + 1);

    }



    /* Delete Job Dialog */

    function closeDeleteDialog() {
        setIsDeletePayRunDialogOpen(false);
    }

    async function deletePayRun() {
        closeDeleteDialog();
        if (clickedPayRun) {
            const response = await PayRunService.Delete(clickedPayRun.payRunId);
            if (!response.error) {
                setShowSnackbar(true);
                setSnackbarMessage('Successfully deleted payrun');
                setSnackbarSeverity('success');
                // Refresh table data
                setRefreshTableTrigger(trigger => trigger + 1);
            } else {
                setShowSnackbar(true);
                setSnackbarMessage('Failed to delete payrun');
                setSnackbarSeverity('error');
            }
        }
        setClickedPayRun(undefined);
    }

    const Dialogs = () => {
        return (
            <>
                {/* Add New Job Dialog */}
                <DraggableDialog title="Create New Pay Run" isOpen={isAddPayRunDialogOpen} onCancel={() => setIsAddPayRunDialogOpen(false)}>
                    <PayRunForm
                        onCancel={() => setIsAddPayRunDialogOpen(false)}
                        onSave={onAddPayRunDialogSave}
                    />
                </DraggableDialog>

                {/* Delete User Dialog */}
                <YesNoDialog
                    title="Delete Pay Run"
                    bodyText="Are you sure? This cannot be reversed"
                    isOpen={isDeletePayRunDialogOpen}
                    onNo={closeDeleteDialog}
                    onYes={deletePayRun}
                />
            </>
        );
    }

    const RowMenu = (props: { rowData: GetPayRunCDto }) => {
        const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
        const isOpen = Boolean(anchorEl);
        const openMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
            event.stopPropagation();
            setAnchorEl(event.currentTarget);
        };
        const handleClose = (event: React.MouseEvent<HTMLButtonElement>) => {
            event.stopPropagation();
            setAnchorEl(null);
        };
        const handleStatus = (event: React.MouseEvent<HTMLButtonElement>, status: string) => {
            event.stopPropagation();

            PayRunService.Update({ ...props.rowData, statusCode: status }).then((response) => {
                if (!response.error) {
                    setRefreshTableTrigger(trigger => trigger + 1);
                }
            });
            setAnchorEl(null);
        }

        const handleDelete = (event: React.MouseEvent) => {
            event.stopPropagation();
            setAnchorEl(null);
            setClickedPayRun(props.rowData);
            setIsDeletePayRunDialogOpen(true);
        };

        return (
            <>
                <IconButton
                    id="actions-list"
                    aria-controls={isOpen ? 'basic-menu' : undefined}
                    aria-haspopup="true"
                    aria-expanded={isOpen ? 'true' : undefined}
                    onClick={openMenu}
                >
                    <FontAwesomeIcon styleName="row-menu-icon" icon="ellipsis-v" />
                </IconButton>
                <Menu
                    id="actions-menu"
                    anchorEl={anchorEl}
                    open={isOpen}
                    onClose={handleClose}
                    MenuListProps={{ 'aria-labelledby': 'menu-button' }}
                >
                    <MenuItem onClick={handleDelete}>
                        <ListItemText>Delete</ListItemText>
                    </MenuItem>
                    <MenuItem component="button" onClick={(event: React.MouseEvent<HTMLButtonElement>) => handleStatus(event, 'Paid')}>
                        <ListItemText>Paid</ListItemText>
                    </MenuItem>
                    <MenuItem component="button" onClick={(event: React.MouseEvent<HTMLButtonElement>) => handleStatus(event, 'Created')}>
                        <ListItemText>Created</ListItemText>
                    </MenuItem>
                </Menu>
            </>
        );
    }

    return (
        <div style={{ overflow: 'hidden' }}>
            <div styleName="card">
                <DataTable<GetListPayRunCDto, "payRunId">
                    primaryKeyProperty="payRunId"
                    title="Pay Run History"
                    tableId='base-PayRunManagement-'
                    pageSize={10}
                    initialSortColumn="CreatedOn"
                    refreshTableTrigger={refreshTableTrigger}
                    tableHeight={600}
                    addButtonLabel="Create New Pay Run"
                    addButtonOnClick={() => setIsAddPayRunDialogOpen(true)}
                    tableHeaders={tableHeaders}
                    renderTableRow={renderTableRow}
                    onRowClick={(row) => navigate(`/PayRunHistory/${row.payRunId}`)}
                    callService={(inListParameters) => PayRunService.List(inListParameters)}
                hideSearch={true}
                />

                <Dialogs />

                <Snackbar open={showSnackbar}
                    autoHideDuration={4000}
                    onClose={() => setShowSnackbar(false)}
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}>
                    <Alert onClose={() => setShowSnackbar(false)} severity={snackbarSeverity}>
                        {snackbarMessage}
                    </Alert>
                </Snackbar>
            </div>
        </div>
    );
}

export default PayRunHistory;
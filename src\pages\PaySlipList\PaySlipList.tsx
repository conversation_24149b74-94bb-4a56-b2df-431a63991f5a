import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Alert, AlertColor, IconButton, ListItemText, Menu, MenuItem, Snackbar, TableCell } from "@mui/material";
import { Suspense, useState } from "react";
import { useNavigate, useParams } from 'react-router-dom';
import { GetPaySlipCDto, GetListPaySlipCDto } from "redi-types";
import DataTable, { TableHeader } from "../../components/DataTable/DataTable";
import DraggableDialog from "../../components/DraggableDialog/DraggableDialog";
import YesNoDialog from "../../components/YesNoDialog/YesNoDialog";
import { dateFormatOptions } from "../../utils/dateFormOptions";
import { NIL as NIL_UUID, v4 as uuidv4 } from 'uuid';

import './styles.scss';
import PaySlipService from '../../services/paySlip';
import withAsyncLoad from '../../components/HOC/withAsyncLoad';
import EmailPDFButton from '../../components/EmailPDFButton/EmailPDFButton';

const PDFFetchButton = withAsyncLoad<{ paySlipId: string }>(() => import('commoncomponents/PDFFetchButton'));



interface Props {
}

const initialValues: GetPaySlipCDto = {
    paySlipId: NIL_UUID,
    payRunId: NIL_UUID,
    totalIncGst: 0,
    gst: 0,
    totalDeductions: 0,
    contractorId: NIL_UUID,
    contractorName: '',
};


function PaySlipList(props: Props) {

    const { id: payRunId } = useParams();   // Url Params

    const navigate = useNavigate();

    const title = payRunId ? 'Pay Run Details' : 'All Pay Slips';

    const [refreshTableTrigger, setRefreshTableTrigger] = useState(0);

    const [clickedPaySlip, setClickedPaySlip] = useState<GetPaySlipCDto>();


    // Dialog
    const [isAddPaySlipDialogOpen, setIsAddPaySlipDialogOpen] = useState(false);
    const [isDeletePaySlipDialogOpen, setIsDeletePaySlipDialogOpen] = useState(false);
    const [isEditPaySlipDialogOpen, setIsEditPaySlipDialogOpen] = useState(false);

    // Snackbar
    const [showSnackbar, setShowSnackbar] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');
    const [snackbarSeverity, setSnackbarSeverity] = useState<AlertColor | undefined>();

    // currnency format regex
    function currencyFormat(num: number) {
        return '$' + num.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
    }

    const tableHeaders: TableHeader[] = [
        { id: 'createdOn', label: 'Created' },
        { id: 'contractorName', label: 'Driver' },
        { id: 'totalDeductions', label: 'Deductions', align: "right" },
        { id: 'totalIncGst', label: 'RCTI (incl. GST)', align: "right" },
        { id: 'menu', label: 'Actions', align: 'right' } // Blank for row menu button
    ];

    function renderTableRow(data: GetListPaySlipCDto) {
        return (
            <>
                {/* Primary column should be bolded (Does not always mean the far left column) */}

                <TableCell>
                    {
                        data?.createdOn
                            ? new Date(data.createdOn).toLocaleDateString('en-AU', dateFormatOptions)
                            : 'N/A'
                    }
                </TableCell>
                <TableCell>{data.contractorName}</TableCell>
                {/* <TableCell align="right">{currencyFormat(data.totalIncGst)}</TableCell> */}
                <TableCell align="right">{currencyFormat(Number(data.totalDeductions))}</TableCell>
                <TableCell align="right">{currencyFormat(data.totalIncGst - (data.totalDeductions ? data.totalDeductions : 0))}</TableCell>
                <TableCell align="right">
                    <RowMenu rowData={data} />
                </TableCell>
            </>
        );
    };

    /* Add Job Dialog */

    async function onAddPaySlipDialogSave(message: string, severity: AlertColor) {
        setIsAddPaySlipDialogOpen(false);
        setShowSnackbar(true);
        setSnackbarMessage(message);
        setSnackbarSeverity(severity);
        setRefreshTableTrigger(trigger => trigger + 1);

    }



    /* Delete Job Dialog */

    function closeDeleteDialog() {
        setIsDeletePaySlipDialogOpen(false);
    }

    async function deletePaySlip() {
        closeDeleteDialog();
        if (clickedPaySlip) {
            const response = await PaySlipService.Delete(clickedPaySlip.paySlipId);
            if (!response.error) {
                setShowSnackbar(true);
                setSnackbarMessage('Successfully deleted payrun');
                setSnackbarSeverity('success');
                // Refresh table data
                setRefreshTableTrigger(trigger => trigger + 1);
            } else {
                setShowSnackbar(true);
                setSnackbarMessage('Failed to delete user');
                setSnackbarSeverity('error');
            }
        }
        setClickedPaySlip(undefined);
    }

    const Dialogs = () => {
        return (
            <>
                {/* Delete User Dialog */}
                <YesNoDialog
                    title="Delete Pay Slip"
                    bodyText="Are you sure? This cannot be reversed"
                    isOpen={isDeletePaySlipDialogOpen}
                    onNo={closeDeleteDialog}
                    onYes={deletePaySlip}
                />
            </>
        );
    }

    const RowMenu = (props: { rowData: GetPaySlipCDto }) => {
        const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
        const isOpen = Boolean(anchorEl);
        const openMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
            event.stopPropagation();
            setAnchorEl(event.currentTarget);
        };
        const handleClose = (event: React.MouseEvent<HTMLButtonElement>) => {
            event.stopPropagation();
            setAnchorEl(null);
        };
        const handleDelete = (event: React.MouseEvent) => {
            event.stopPropagation();
            setAnchorEl(null);
            setClickedPaySlip(props.rowData);
            setIsDeletePaySlipDialogOpen(true);
        };

        return (
            <>
                    <PDFFetchButton paySlipId={props.rowData.paySlipId} />
                <EmailPDFButton paySlipId={props.rowData.paySlipId} />
                <IconButton
                    id="actions-list"
                    aria-controls={isOpen ? 'basic-menu' : undefined}
                    aria-haspopup="true"
                    aria-expanded={isOpen ? 'true' : undefined}
                    onClick={openMenu}
                >
                    <FontAwesomeIcon styleName="row-menu-icon" icon="ellipsis-v" />
                </IconButton>
                <Menu
                    id="actions-menu"
                    anchorEl={anchorEl}
                    open={isOpen}
                    onClose={handleClose}
                    MenuListProps={{ 'aria-labelledby': 'menu-button' }}
                >
                    <MenuItem onClick={handleDelete}>
                        <ListItemText>Delete</ListItemText>
                    </MenuItem>
                </Menu>
            </>
        );
    }



    return (
        <div style={{ overflow: 'hidden' }}>
            <div styleName="card">
                <DataTable<GetListPaySlipCDto, "paySlipId">
                    primaryKeyProperty="paySlipId"
                    title="Payslips"
                    tableId='base-PaySlipManagement-'
                    pageSize={10}
                    initialSortColumn="CreatedOn"
                    refreshTableTrigger={refreshTableTrigger}
                    tableHeight={600}
                    // addButtonLabel="Create New Pay Slip"
                    // addButtonOnClick={() => setIsAddPaySlipDialogOpen(true)}
                    tableHeaders={tableHeaders}
                    renderTableRow={renderTableRow}
                    callService={(inListParameters, inSearch) => PaySlipService.List(inListParameters, payRunId || '', inSearch)}
                />

                <Dialogs />

                <Snackbar open={showSnackbar}
                    autoHideDuration={4000}
                    onClose={() => setShowSnackbar(false)}
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}>
                    <Alert onClose={() => setShowSnackbar(false)} severity={snackbarSeverity}>
                        {snackbarMessage}
                    </Alert>
                </Snackbar>
            </div>
        </div>
    );
}

export default PaySlipList;
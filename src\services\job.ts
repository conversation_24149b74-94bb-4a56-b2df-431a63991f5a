import { http, HttpPromise, HttpResult } from "redi-http";
import { DataSourceResult, PagingParameters } from "redi-query-builder";
import { GetJobCDto, ListResponseDto } from "redi-types";
import config from "../config/config";
import { StandardListParameters } from "redi-types";
import { JobStatusCodeEnum } from "../enum/JobStatusCoceEnum";

const route = "Job/";

export default class JobService {

	static Get(jobId: string): Promise<HttpResult<GetJobCDto>> {
		let url = `${config.apiURL + route}Get`;

		return http({ url, method: "GET", jobId })
			.then(data => data)
			.catch(error => error);
	}

	
  static getList(
		standardListParameters?: StandardListParameters,
		query?: string,
		statusCode?: string
	): HttpPromise<ListResponseDto<GetJobCDto>> {
		const url = `${config.apiURL + route}GetList`;
		const promise = http<ListResponseDto<GetJobCDto>>({ url, method: "GET", query, statusCode, standardListParameters });
		promise.catch(error => error);
		return promise;
	}

	static List(
		standardListParameters?: StandardListParameters,
		query?: string,
		statusCode?: string
	): Promise<HttpResult<ListResponseDto<GetJobCDto>>> {
		let url = `${config.apiURL + route}GetList`;

		return http({ url, method: "GET", standardListParameters, query, statusCode })
			.catch(error => error);
	}


	static Create(data: GetJobCDto): Promise<HttpResult<GetJobCDto>> {
		let url = `${config.apiURL + route}Create`;

		return http({ url, method: "POST", data })
			.then(data => data)
			.catch(error => error);
	}


	static Update(data: GetJobCDto): Promise<HttpResult<GetJobCDto>> {
		let url = `${config.apiURL + route}Update`;

		return http({ url, method: "POST", data })
			.then(data => data)
			.catch(error => error);
	}

	static Delete(jobId: string): Promise<HttpResult> {
		let url = `${config.apiURL + route}Delete`;

		return http({ url, method: "POST", jobId })
			.then(data => data)
			.catch(error => error);
	}
	static UnDelete(jobId: string): Promise<HttpResult> {
		let url = `${config.apiURL + route}UnDelete`;

		return http({ url, method: "POST", jobId })
			.then(data => data)
			.catch(error => error);
	}

	static UpdateListStatus(jobList: GetJobCDto[], statusCode: JobStatusCodeEnum): Promise<HttpResult> {
		const url = `${config.apiURL + route}UpdateListStatus`;

		return http({ url, method: "POST", data: jobList, statusCode })
			.then(data => data)
			.catch(error => error);
	}


}

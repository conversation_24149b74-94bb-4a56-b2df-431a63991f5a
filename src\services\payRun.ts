import { http, HttpResult } from "redi-http";
import { GetPayRunCDto, ListResponseDto } from "redi-types";
import config from "../config/config";
import { StandardListParameters } from "redi-types";

const route = "PayRun/";

export default class PayRunService {

	static Get(payRunId: string): Promise<HttpResult<GetPayRunCDto>> {
		let url = `${config.apiURL + route}Get`;

		return http({ url, method: "GET", payRunId })
			.then(data => data)
			.catch(error => error);
	}

	static List(
		standardListParameters?: StandardListParameters,
		query?: string,
		statusCode?: string
	): Promise<HttpResult<ListResponseDto<GetPayRunCDto>>> {
		let url = `${config.apiURL + route}GetList`;

		return http({ url, method: "GET", standardListParameters, query, statusCode })
			.catch(error => error);
	}


	// static Create(data: GetPayRunCDto): Promise<HttpResult<GetPayRunCDto>> {
	// 	let url = `${config.apiURL + route}Create`;

	// 	return http({ url, method: "POST", data })
	// 		.then(data => data)
	// 		.catch(error => error);
	// }


	static Update(data: GetPayRunCDto): Promise<HttpResult<GetPayRunCDto>> {
		let url = `${config.apiURL + route}Update`;

		return http({ url, method: "POST", data })
			.then(data => data)
			.catch(error => error);
	}

	static Delete(payRunId: string): Promise<HttpResult> {
		let url = `${config.apiURL + route}Delete`;

		return http({ url, method: "POST", payRunId })
			.then(data => data)
			.catch(error => error);
	}
	static UnDelete(payRunId: string): Promise<HttpResult> {
		let url = `${config.apiURL + route}UnDelete`;

		return http({ url, method: "POST", payRunId })
			.then(data => data)
			.catch(error => error);
	}

	static CreatePayRun(fromDate: Date, toDate: Date): Promise<HttpResult> {
		let url = `${config.apiURL + route}CreatePayRun`;

		return http({ url, method: "POST", fromDate, toDate })
			.then(data => data)
			.catch(error => error);
	}
}

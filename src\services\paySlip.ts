import { http, HttpResult } from "redi-http";
import { GetPaySlipCDto, ListResponseDto } from "redi-types";
import config from "../config/config";
import { StandardListParameters } from "redi-types";
import { showToast } from "redi-formik-material";

const route = "PaySlip/";

export default class PaySlipService {

	static Get(paySlipId: string): Promise<HttpResult<GetPaySlipCDto>> {
		let url = `${config.apiURL + route}Get`;

		return http({ url, method: "GET", paySlipId })
			.then(data => {
				showToast("success", "PaySlip retrieved successfully");
				return data;
			})
			.catch(error => {
				showToast("error", "Failed to retrieve PaySlip");
				return error;
			});
	}

	static List(
		standardListParameters?: StandardListParameters,
		payRunId?: string,
		query?: string,
		statusCode?: string,
	): Promise<HttpResult<ListResponseDto<GetPaySlipCDto>>> {
		let url = `${config.apiURL + route}GetList`;

		return http({ url, method: "GET", standardListParameters, payRunId, query, statusCode })
			.then(data => {
				showToast("success", "PaySlip list retrieved successfully");
				return data;
			})
			.catch(error => {
				showToast("error", "Failed to retrieve PaySlip list");
				return error;
			});
	}


	static Create(data: GetPaySlipCDto): Promise<HttpResult<GetPaySlipCDto>> {
		let url = `${config.apiURL + route}Create`;

		return http({ url, method: "POST", data })
			.then(data => {
				showToast("success", "PaySlip created successfully");
				return data;
			})
			.catch(error => {
				showToast("error", "Failed to create PaySlip");
				return error;
			});
	}


	static Update(data: GetPaySlipCDto): Promise<HttpResult<GetPaySlipCDto>> {
		let url = `${config.apiURL + route}Update`;

		return http({ url, method: "POST", data })
			.then(data => {
				showToast("success", "PaySlip updated successfully");
				return data;
			})
			.catch(error => {
				showToast("error", "Failed to update PaySlip");
				return error;
			});
	}

	static Delete(paySlipId: string): Promise<HttpResult> {
		let url = `${config.apiURL + route}Delete`;

		return http({ url, method: "POST", paySlipId })
			.then(data => {
				showToast("success", "PaySlip deleted successfully");
				return data;
			})
			.catch(error => {
				showToast("error", "Failed to delete PaySlip");
				return error;
			});
	}
	static UnDelete(paySlipId: string): Promise<HttpResult> {
		let url = `${config.apiURL + route}UnDelete`;

		return http({ url, method: "POST", paySlipId })
			.then(data => {
				showToast("success", "PaySlip undeleted successfully");
				return data;
			})
			.catch(error => {
				showToast("error", "Failed to undelete PaySlip");
				return error;
			});
	}
	static emailPayslip(paySlipId: string): Promise<HttpResult> {
		let url = `${config.apiURL + route}EmailPayslip`;

		return http({ url, method: "POST", paySlipId })
			.then(data => {
				showToast("success", "Payslip sent successfully");
				return data;
			})
			.catch(error => {
				showToast("error", "Failed to send Payslip");
				console.log(error);
				return error;
			});
	}

}
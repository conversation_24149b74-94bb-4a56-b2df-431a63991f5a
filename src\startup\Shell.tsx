import { ThemeProvider } from "@emotion/react";
import { Link, Route, Routes } from "react-router-dom";
import logo from '../assets/rediLogo.png';
import { mainTheme } from "../config/theme/mainTheme";
import './styles.scss';

function Shell(props: Props) {

  const { routes } = props;

  return (
    <ThemeProvider theme={mainTheme}>
      <header>
        <img src={logo} styleName="app-logo" alt="logo" title="Josh1" />
        <p>Micro Frontend Base</p>
        <Link styleName="link" to="">Components Demo</Link>
        <Link styleName="link" to="UserManagement">Table Example (User)</Link>
        <Link styleName="link" to="JobManagement">Job List</Link>
        <Link styleName="link" to="PayRunHistory">PayRun History</Link>
        <Link styleName="link" to="PaySlipList">PaySlip List(ALL)</Link>
      </header>
      <div>
        <Routes>
          {
            routes.map((a) => (
              <Route key={a.path} path={a.path} element={a.element} />
            ))
          }
        </Routes>
      </div>
    </ThemeProvider>
  );
}

interface Props {
  routes: {
    path: string;
    name: string;
    element: JSX.Element;
  }[];
}

export default Shell; //To test Authenticate add withAuthenticate(Shell, { options here... })